#!/usr/bin/env python3
"""
FastAPI application for Rethink BH to Supabase sync.
Designed for Google Cloud Run deployment with webhook support.
"""

import os
import logging
import traceback
from datetime import datetime
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware

from rethink_sync import RethinkSync, RethinkSyncError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Rethink BH Sync API",
    description="Automated sync service for Rethink Behavioral Health to Supabase",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware for browser compatibility
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Optional simple authorization
AUTH_KEY = os.getenv("API_AUTH_KEY")

def check_auth(request: Request) -> bool:
    """Simple authorization check if AUTH_KEY is set."""
    if not AUTH_KEY:
        return True  # No auth required if key not set
    
    # Check for auth key in query params or headers
    auth_from_query = request.query_params.get("auth_key")
    auth_from_header = request.headers.get("X-Auth-Key")
    
    return auth_from_query == AUTH_KEY or auth_from_header == AUTH_KEY

@app.exception_handler(RethinkSyncError)
async def rethink_sync_exception_handler(request: Request, exc: RethinkSyncError):
    """Handle RethinkSyncError exceptions."""
    logger.error(f"Sync error: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            "status": "error",
            "message": "Sync operation failed",
            "timestamp": datetime.now().isoformat()
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle unexpected exceptions."""
    logger.error(f"Unexpected error: {str(exc)}")
    logger.error(traceback.format_exc())
    return JSONResponse(
        status_code=500,
        content={
            "status": "error",
            "message": "Internal server error",
            "timestamp": datetime.now().isoformat()
        }
    )

@app.get("/")
async def root():
    """Root endpoint with basic service information."""
    return {
        "service": "Rethink BH Sync API",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "sync": "/run",
            "health": "/health",
            "docs": "/docs"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint for Cloud Run."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "rethink-sync"
    }

@app.get("/run")
async def run_sync(request: Request) -> Dict[str, Any]:
    """
    Execute the complete Rethink BH to Supabase sync process.
    
    This endpoint:
    1. Authenticates with Rethink BH
    2. Downloads the latest appointment data (6 months back to end of current month)
    3. Truncates and resets the Supabase rethinkDump table
    4. Inserts all appointment data
    5. Returns a status report
    
    Optional Authorization:
    - Query parameter: ?auth_key=YOUR_KEY
    - Header: X-Auth-Key: YOUR_KEY
    
    Returns:
        JSON response with sync status, row counts, and any errors
    """
    # Check authorization if enabled
    if not check_auth(request):
        logger.warning("Unauthorized sync attempt")
        raise HTTPException(
            status_code=401,
            detail="Unauthorized: Invalid or missing auth key"
        )
    
    start_time = datetime.now()
    logger.info(f"Sync request received at {start_time.isoformat()}")
    
    try:
        # Initialize sync service
        sync_service = RethinkSync()
        
        # Execute sync
        result = sync_service.run_sync()
        
        # Add timing information
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        response = {
            **result,
            "timestamp": end_time.isoformat(),
            "duration_seconds": round(duration, 2),
            "message": "Sync completed successfully"
        }
        
        logger.info(f"Sync completed successfully in {duration:.2f}s: {result['rows_inserted']} rows inserted")
        return response
        
    except RethinkSyncError as e:
        # Re-raise to be handled by exception handler
        raise e
    except Exception as e:
        # Log and re-raise unexpected errors
        logger.error(f"Unexpected error in sync endpoint: {str(e)}")
        raise e

@app.post("/run")
async def run_sync_post(request: Request) -> Dict[str, Any]:
    """
    POST version of the sync endpoint for webhook compatibility.
    Accepts the same parameters as the GET version.
    """
    return await run_sync(request)

# Cloud Run requires the app to listen on the PORT environment variable
if __name__ == "__main__":
    import uvicorn
    
    port = int(os.getenv("PORT", 8080))
    host = os.getenv("HOST", "0.0.0.0")
    
    logger.info(f"Starting server on {host}:{port}")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        log_level="info",
        access_log=True
    )
