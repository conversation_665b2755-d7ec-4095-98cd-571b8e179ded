# 🏥 Rethink BH Automation

Automated appointment data sync from Rethink Behavioral Health to Supabase database, deployable to Google Cloud Run for webhook automation.

## 🎯 Overview

This tool automates the daily download of appointment data from Rethink BH and ingests it into your Supabase PostgreSQL database. Available as both local scripts and a Cloud Run-hosted webhook service for integration with automation platforms like Make.com.

## ⚡ Quick Start

### 🚀 Cloud Run Deployment (Recommended)

1. **Deploy to Google Cloud:**
   ```bash
   ./deploy.sh YOUR_PROJECT_ID us-central1
   ./setup-secrets.sh YOUR_PROJECT_ID
   ```

2. **Configure secrets:**
   ```bash
   gcloud secrets versions add RTHINK_USER --data-file=<(echo 'your-email')
   gcloud secrets versions add RTHINK_PASS --data-file=<(echo 'your-password')
   gcloud secrets versions add SUPABASE_DB_URL --data-file=<(echo 'your-db-url')
   ```

3. **Test the webhook:**
   ```bash
   curl https://your-service-url.run.app/run
   ```

See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed deployment instructions.

### 💻 Local Development

#### Prerequisites
- Python 3.11+
- uv package manager
- Supabase database access

#### Setup
1. Install dependencies:
   ```bash
   uv sync
   ```

2. Configure environment variables in `.env`:
   ```
   RTHINK_USER=<EMAIL>
   RTHINK_PASS=your_password
   SUPABASE_DB_URL=postgresql://user:pass@host:port/db
   ```

## 📊 Usage

### Option 1: Complete Automation (Recommended)
Run the complete workflow automatically (no prompts - webhook-ready):
```bash
uv run python run_automation.py
```

### Option 2: Individual Scripts

#### Download Appointments
Download Excel files directly from Rethink BH (browser-compliant, 217 rows):
```bash
uv run python download_simple.py
```

#### Ingest to Supabase
Import Excel data into your Supabase database:
```bash
uv run python ingest_appointments.py
```

## 📁 Files

### Cloud Run Service
- `main.py` - **FastAPI application** - Webhook endpoint for Cloud Run
- `rethink_sync.py` - **Unified sync logic** - Combined download and ingestion
- `Dockerfile` - Container configuration for Cloud Run
- `requirements.txt` - Python dependencies for Docker
- `deploy.sh` - Automated deployment script
- `setup-secrets.sh` - Secret Manager configuration script
- `DEPLOYMENT.md` - Detailed deployment guide

### Local Scripts
- `run_automation.py` - **Main script** - Complete workflow with interactive menu
- `download_simple.py` - Downloads Excel files from Rethink BH API (saves to `downloads/` folder)
- `ingest_appointments.py` - Ingests Excel data into Supabase (with daily table refresh)
- `downloads/` - Folder containing downloaded Excel files with timestamps
- `pyproject.toml` - Project dependencies and configuration

## 🔧 Features

### Cloud Run Service
- **Webhook-ready API** - RESTful endpoint for automation platforms
- **Google Cloud Secret Manager** - Secure credential storage
- **Structured logging** - Cloud Logging integration
- **Health checks** - Built-in monitoring endpoints
- **Auto-scaling** - Handles variable workloads
- **Memory-efficient** - In-memory processing without file I/O

### Core Functionality
- **Browser-compliant downloads** - Matches exact browser behavior (~217 rows)
- **Daily table refresh** - Truncates and reloads data for up-to-date appointments
- **Sequential ID reset** - Auto-increment IDs reset to 1-N on each import
- **Handles recurring appointments** - Preserves all instances with duplicate appointmentIDs
- **Robust error handling** - Comprehensive logging and error recovery
- **6-month date range** - Automatically calculates optimal date range

### Local Development
- **Interactive automation** - Complete workflow with progress tracking
- **Organized file storage** - Downloads saved to timestamped files in `downloads/` folder
- **Webhook-ready scripts** - No prompts or interruptions for automation

## 📋 Database Schema

The `rethinkDump` table includes:
- Auto-incrementing primary key (`id`)
- All appointment fields (type, location, client, staff, etc.)
- Supports duplicate `appointmentID`s for recurring series

## 🌐 API Endpoints (Cloud Run)

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/` | GET | Service information |
| `/health` | GET | Health check |
| `/run` | GET/POST | Execute sync |
| `/docs` | GET | API documentation |

## 🔗 Integration

### Make.com Webhook
1. Create HTTP Request module
2. Set URL to: `https://your-service-url.run.app/run`
3. Method: GET or POST
4. Schedule: Daily at preferred time

### Google Cloud Scheduler (Alternative)
```bash
gcloud scheduler jobs create http rethink-sync-daily \
  --schedule "0 3 * * *" \
  --uri https://your-service-url.run.app/run \
  --http-method GET
```

## 📚 Documentation

- [DEPLOYMENT.md](DEPLOYMENT.md) - Complete deployment guide
- [PRD.md](PRD.md) - Project requirements document
- API docs available at `/docs` endpoint when deployed

