import os
import psycopg2
from urllib.parse import urlparse
from dotenv import load_dotenv
import pandas as pd

# Load environment variables
load_dotenv()
db_url = os.getenv("SUPABASE_DB_URL")

if not db_url:
    raise EnvironmentError("SUPABASE_DB_URL not found in environment variables")

print("🔗 Connecting to Supabase database...")

# Parse connection URI
parsed = urlparse(db_url)
conn = psycopg2.connect(
    dbname=parsed.path[1:],  # Remove leading slash
    user=parsed.username,
    password=parsed.password,
    host=parsed.hostname,
    port=parsed.port
)

print("✅ Connected to database")

# Clear existing data and reset auto-increment IDs
print("\n🔄 Truncating table and resetting IDs for fresh daily import...")
with conn.cursor() as cur:
    # Use TRUNCATE instead of DELETE for better performance and auto-reset of sequences
    cur.execute("TRUNCATE TABLE rethinkDump RESTART IDENTITY;")
    conn.commit()
print("✅ Table truncated and ID sequence reset to 1")

# Find and load Excel file
import glob

# Look for Excel files in the downloads directory
downloads_dir = "downloads"
if not os.path.exists(downloads_dir):
    raise FileNotFoundError(f"Downloads folder '{downloads_dir}' not found. Please run download_simple.py first.")

excel_pattern = os.path.join(downloads_dir, "*.xlsx")
excel_files = glob.glob(excel_pattern)

if not excel_files:
    raise FileNotFoundError(f"No Excel files found in '{downloads_dir}' folder. Please run download_simple.py first.")

# Use the most recent Excel file
excel_file = max(excel_files, key=os.path.getmtime)
print(f"📁 Found {len(excel_files)} Excel file(s) in downloads folder")
print(f"📊 Using most recent: {os.path.basename(excel_file)}")

print(f"\n📊 Loading Excel file: {excel_file}")

# Load Excel file - skip the empty first row
df = pd.read_excel(excel_file, skiprows=1)
print(f"📈 Loaded {len(df)} rows from Excel")

# Column mapping (same as before)
COLUMN_MAPPING = {
    'Appointment Type': 'appointmentType',
    'Appointment Tag': 'appointmentTag', 
    'Service Line': 'serviceLine',
    'Service': 'service',
    'Appointment Location': 'appointmentLocation',
    'Duration': 'duration',
    'Day': 'day',
    'Date': 'date',
    'Time': 'time',
    'Scheduled Date': 'scheduledDate',
    'Modified Date': 'modifiedDate',
    'Client': 'client',
    'Staff Member': 'staff',
    'Status': 'status',
    'Session Note': 'sessionNote',
    'Staff Verification': 'staffVerification',
    'Staff Verification Address': 'staffVerificationAddress',
    'Guardian Verification': 'guardianVerification',
    'Parent Verification Address': 'parentVerificationAddress',
    'PayCode Name': 'paycodeName',
    'PayCode': 'paycode',
    'Notes': 'notes',
    'Appointment ID': 'appointmentID',
    'Validation': 'validation',
    'Place of Service': 'placeOfService'
}

def map_excel_to_db_columns(df):
    """Map Excel columns to database columns."""
    mapped_columns = {}
    for excel_col in df.columns:
        if excel_col in COLUMN_MAPPING:
            db_col = COLUMN_MAPPING[excel_col]
            mapped_columns[db_col] = excel_col
    return mapped_columns

def prepare_row_data(row, column_mapping):
    """Prepare a single row for database insertion."""
    db_columns = [
        'appointmentType', 'appointmentTag', 'serviceLine', 'service',
        'appointmentLocation', 'duration', 'day', 'date', 'time',
        'scheduledDate', 'modifiedDate', 'client', 'staff', 'status',
        'sessionNote', 'staffVerification', 'staffVerificationAddress',
        'guardianVerification', 'parentVerificationAddress',
        'paycodeName', 'paycode', 'notes', 'appointmentID',
        'validation', 'placeOfService'
    ]
    
    values = []
    for db_col in db_columns:
        if db_col in column_mapping:
            excel_col = column_mapping[db_col]
            value = row[excel_col]
            if pd.isna(value):
                values.append(None)
            else:
                values.append(value)
        else:
            values.append(None)
    
    return values

# Insert all data (including duplicates)
print("\n🔄 Mapping columns and inserting data...")
column_mapping = map_excel_to_db_columns(df)

success_count = 0
error_count = 0

with conn.cursor() as cur:
    for index, row in df.iterrows():
        try:
            values = prepare_row_data(row, column_mapping)
            
            # Insert without conflict handling - allows duplicates
            cur.execute("""
                INSERT INTO rethinkDump (
                    appointmentType, appointmentTag, serviceLine, service,
                    appointmentLocation, duration, day, date, time,
                    scheduledDate, modifiedDate, client, staff, status,
                    sessionNote, staffVerification, staffVerificationAddress,
                    guardianVerification, parentVerificationAddress,
                    paycodeName, paycode, notes, appointmentID,
                    validation, placeOfService
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s
                )
            """, values)
            
            success_count += 1
            
            if (index + 1) % 100 == 0:
                print(f"📈 Processed {index + 1}/{len(df)} rows...")
                
        except Exception as e:
            error_count += 1
            print(f"❌ Error processing row {index + 1}: {e}")
            continue

conn.commit()
conn.close()

print(f"\n🎉 Daily data refresh completed!")
print(f"✅ Successfully inserted: {success_count} rows")
print(f"❌ Errors encountered: {error_count} rows")
print(f"📊 Total processed: {len(df)} rows")

# Delete the Excel file after successful ingestion to prevent re-processing
try:
    os.remove(excel_file)
    print(f"🗑️  Deleted processed Excel file: {os.path.basename(excel_file)}")
except Exception as e:
    print(f"⚠️  Could not delete Excel file: {e}")

print(f"\n📋 Your Supabase table is now up-to-date with {success_count} appointments!")
print(f"   ✅ Table truncated and refreshed with latest data")
print(f"   🔢 IDs reset to sequential 1-{success_count}")
print(f"   🗑️  Excel file deleted to prevent re-processing")
print(f"   📅 Ready for tomorrow's fresh import")
