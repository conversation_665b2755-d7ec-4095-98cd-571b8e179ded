#!/bin/bash

# Deployment script for Rethink BH Sync to Google Cloud Run
# Usage: ./deploy.sh [PROJECT_ID] [REGION]

set -e

# Configuration
PROJECT_ID=${1:-"your-project-id"}
REGION=${2:-"us-central1"}
SERVICE_NAME="rethink-sync"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

echo "🚀 Deploying Rethink BH Sync to Google Cloud Run"
echo "   Project: ${PROJECT_ID}"
echo "   Region: ${REGION}"
echo "   Service: ${SERVICE_NAME}"
echo ""

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI is not installed. Please install it first."
    exit 1
fi

# Set the project
echo "📋 Setting project to ${PROJECT_ID}..."
gcloud config set project ${PROJECT_ID}

# Enable required APIs
echo "🔧 Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable secretmanager.googleapis.com

# Build and push the container image
echo "🏗️  Building container image..."
gcloud builds submit --tag ${IMAGE_NAME}

# Deploy to Cloud Run
echo "🚀 Deploying to Cloud Run..."
gcloud run deploy ${SERVICE_NAME} \
    --image ${IMAGE_NAME} \
    --platform managed \
    --region ${REGION} \
    --allow-unauthenticated \
    --port 8080 \
    --memory 1Gi \
    --cpu 1 \
    --timeout 300 \
    --max-instances 10 \
    --set-env-vars GOOGLE_CLOUD_PROJECT=${PROJECT_ID}

# Get the service URL
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} --region=${REGION} --format='value(status.url)')

echo ""
echo "✅ Deployment completed successfully!"
echo "   Service URL: ${SERVICE_URL}"
echo "   Health check: ${SERVICE_URL}/health"
echo "   Sync endpoint: ${SERVICE_URL}/run"
echo "   API docs: ${SERVICE_URL}/docs"
echo ""
echo "📋 Next steps:"
echo "   1. Set up secrets in Secret Manager:"
echo "      gcloud secrets create RTHINK_USER --data-file=<(echo 'your-email')"
echo "      gcloud secrets create RTHINK_PASS --data-file=<(echo 'your-password')"
echo "      gcloud secrets create SUPABASE_DB_URL --data-file=<(echo 'your-db-url')"
echo ""
echo "   2. Grant Secret Manager access to Cloud Run:"
echo "      ./setup-secrets.sh ${PROJECT_ID}"
echo ""
echo "   3. Test the endpoint:"
echo "      curl ${SERVICE_URL}/run"
echo ""
echo "   4. Set up Make.com webhook to: ${SERVICE_URL}/run"
