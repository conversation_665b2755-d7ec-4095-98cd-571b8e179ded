#!/bin/bash

# <PERSON><PERSON>t to set up Google Cloud Secret Manager secrets and permissions
# Usage: ./setup-secrets.sh [PROJECT_ID]

set -e

PROJECT_ID=${1:-"your-project-id"}
SERVICE_NAME="rethink-sync"
REGION="us-central1"

echo "🔐 Setting up secrets for Rethink BH Sync"
echo "   Project: ${PROJECT_ID}"
echo ""

# Function to create secret if it doesn't exist
create_secret_if_not_exists() {
    local secret_name=$1
    local secret_description=$2
    
    if gcloud secrets describe ${secret_name} --project=${PROJECT_ID} &>/dev/null; then
        echo "✅ Secret ${secret_name} already exists"
    else
        echo "📝 Creating secret: ${secret_name}"
        gcloud secrets create ${secret_name} \
            --project=${PROJECT_ID} \
            --replication-policy="automatic" \
            --labels="service=rethink-sync"
        echo "   Please add the secret value:"
        echo "   gcloud secrets versions add ${secret_name} --data-file=<(echo 'YOUR_VALUE')"
    fi
}

# Create secrets
echo "📋 Creating required secrets..."
create_secret_if_not_exists "RTHINK_USER" "Rethink BH email address"
create_secret_if_not_exists "RTHINK_PASS" "Rethink BH password"
create_secret_if_not_exists "SUPABASE_DB_URL" "Supabase database connection URL"

# Get Cloud Run service account
echo ""
echo "🔍 Getting Cloud Run service account..."
SERVICE_ACCOUNT=$(gcloud run services describe ${SERVICE_NAME} \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format='value(spec.template.spec.serviceAccountName)' 2>/dev/null || echo "")

if [ -z "$SERVICE_ACCOUNT" ]; then
    # Use default compute service account
    SERVICE_ACCOUNT="${PROJECT_ID}-<EMAIL>"
    echo "   Using default compute service account: ${SERVICE_ACCOUNT}"
else
    echo "   Found service account: ${SERVICE_ACCOUNT}"
fi

# Grant Secret Manager access
echo ""
echo "🔑 Granting Secret Manager access to service account..."
for secret in "RTHINK_USER" "RTHINK_PASS" "SUPABASE_DB_URL"; do
    echo "   Granting access to ${secret}..."
    gcloud secrets add-iam-policy-binding ${secret} \
        --project=${PROJECT_ID} \
        --member="serviceAccount:${SERVICE_ACCOUNT}" \
        --role="roles/secretmanager.secretAccessor"
done

echo ""
echo "✅ Secret setup completed!"
echo ""
echo "📋 To add secret values, run:"
echo "   gcloud secrets versions add RTHINK_USER --data-file=<(echo '<EMAIL>')"
echo "   gcloud secrets versions add RTHINK_PASS --data-file=<(echo 'your-password')"
echo "   gcloud secrets versions add SUPABASE_DB_URL --data-file=<(echo 'postgresql://user:pass@host:port/db')"
echo ""
echo "📋 To verify secrets:"
echo "   gcloud secrets versions list RTHINK_USER"
echo "   gcloud secrets versions list RTHINK_PASS"
echo "   gcloud secrets versions list SUPABASE_DB_URL"
